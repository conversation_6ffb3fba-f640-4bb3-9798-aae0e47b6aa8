{"Version": 3, "Meta": {"Duration": 7, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 9, "TotalSegmentCount": 49, "TotalPointCount": 130, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 1.333, 0, 1, 1.528, 0, 1.722, 30, 1.917, 30, 1, 2.1, 30, 2.283, 0, 2.467, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 1.333, 1, 1, 1.461, 1, 1.589, 0, 1.717, 0, 1, 1.8, 0, 1.883, 1, 1.967, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 1.417, 0, 1, 1.606, 0, 1.794, 1, 1.983, 1, 1, 2.189, 1, 2.394, 0, 2.6, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 1.417, 0, 1, 1.606, 0, 1.794, 0, 1.983, 0, 1, 2.189, 0, 2.394, 0, 2.6, 0, 0, 7, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 1, 0, 7, 1]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 1, 0, 0, 7, 205.714]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 1, 0.167, 0, 0.333, -30, 0.5, -30, 1, 0.667, -30, 0.833, -20, 1, 0, 1, 1.167, 20, 1.333, 30, 1.5, 30, 1, 1.667, 30, 1.833, 20, 2, 0, 1, 2.167, -20, 2.333, -30, 2.5, -30, 1, 2.667, -30, 2.833, -20, 3, 0, 1, 3.167, 20, 3.333, 30, 3.5, 30, 1, 3.667, 30, 3.833, 20, 4, 0, 1, 4.167, -20, 4.333, -30, 4.5, -30, 1, 4.667, -30, 4.833, 0, 5, 0, 1, 5.167, 0, 5.333, -30, 5.5, -30, 1, 5.667, -30, 5.833, -20, 6, 0, 1, 6.167, 20, 6.333, 30, 6.5, 30, 1, 6.667, 30, 6.833, 20, 7, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 0, 0.25, 0, 1, 0.417, 0, 0.583, -30, 0.75, -30, 1, 0.917, -30, 1.083, -20, 1.25, 0, 1, 1.417, 20, 1.583, 30, 1.75, 30, 1, 1.917, 30, 2.083, 20, 2.25, 0, 1, 2.417, -20, 2.583, -30, 2.75, -30, 1, 2.917, -30, 3.083, -20, 3.25, 0, 1, 3.417, 20, 3.583, 30, 3.75, 30, 1, 3.917, 30, 4.083, 20, 4.25, 0, 1, 4.417, -20, 4.583, -30, 4.75, -30, 1, 4.917, -30, 5.083, 0, 5.25, 0, 1, 5.417, 0, 5.583, -30, 5.75, -30, 1, 5.917, -30, 6.083, -20, 6.25, 0, 1, 6.417, 20, 6.583, 30, 6.75, 30, 1, 6.833, 30, 6.917, 27.5, 7, 22.5]}]}